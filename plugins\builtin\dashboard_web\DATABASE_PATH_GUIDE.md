# RunSim Dashboard 数据库路径配置指南

## 概述

RunSim Dashboard 现在支持动态数据库路径配置，解决多用户环境下的权限问题。数据库文件不再固定存储在 `plugins/builtin/dashboard_web/data` 目录下，而是可以根据用户需求灵活配置。

## 路径优先级

系统按以下优先级顺序确定数据库文件路径：

1. **环境变量** `RUNSIM_DB_PATH` (最高优先级)
2. **配置文件** `database_config.json`
3. **当前工作目录** `runsim_dashboard.db`
4. **默认路径** `plugins/builtin/dashboard_web/data/dashboard.db` (向后兼容)

## 配置方法

### 方法1: 使用环境变量 (推荐)

#### Windows
```cmd
# 临时设置 (当前会话有效)
set RUNSIM_DB_PATH=C:\Users\<USER>\runsim_data\dashboard.db

# 永久设置 (系统环境变量)
setx RUNSIM_DB_PATH "C:\Users\<USER>\runsim_data\dashboard.db"
```

#### Linux/Mac
```bash
# 临时设置 (当前会话有效)
export RUNSIM_DB_PATH=/home/<USER>/runsim_data/dashboard.db

# 永久设置 (添加到 ~/.bashrc 或 ~/.zshrc)
echo 'export RUNSIM_DB_PATH=/home/<USER>/runsim_data/dashboard.db' >> ~/.bashrc
```

### 方法2: 使用配置文件

编辑 `plugins/builtin/dashboard_web/database_config.json` 文件：

```json
{
    "database": {
        "path": "C:/Users/<USER>/runsim_data/dashboard.db",
        "description": "数据库文件路径配置"
    }
}
```

### 方法3: 使用配置工具

运行配置工具脚本：

```bash
cd plugins/builtin/dashboard_web
python configure_database_path.py
```

## 路径格式

### 文件路径
直接指定数据库文件的完整路径：
- Windows: `C:\Users\<USER>\runsim_data\dashboard.db`
- Linux/Mac: `/home/<USER>/runsim_data/dashboard.db`

### 目录路径
指定目录，系统会在该目录下创建 `runsim_dashboard.db` 文件：
- Windows: `C:\Users\<USER>\runsim_data\`
- Linux/Mac: `/home/<USER>/runsim_data/`

### 相对路径
相对于当前工作目录的路径：
- `./data/dashboard.db`
- `../shared_data/dashboard.db`

## 权限检查

系统会自动检查目录的写权限：

1. 如果指定目录不存在，系统会尝试创建
2. 如果没有写权限，会回退到默认路径
3. 权限检查可以通过配置文件禁用

## 使用示例

### 示例1: 多用户共享数据库

```bash
# 设置共享目录
export RUNSIM_DB_PATH=/shared/runsim_data/

# 所有用户都会使用 /shared/runsim_data/runsim_dashboard.db
```

### 示例2: 用户个人数据库

```bash
# Windows
set RUNSIM_DB_PATH=%USERPROFILE%\runsim_data\dashboard.db

# Linux/Mac
export RUNSIM_DB_PATH=$HOME/runsim_data/dashboard.db
```

### 示例3: 项目特定数据库

```bash
# 在项目目录下使用独立数据库
cd /path/to/project
export RUNSIM_DB_PATH=./project_dashboard.db
```

## 故障排除

### 问题1: 权限被拒绝

**症状**: 无法创建或写入数据库文件

**解决方案**:
1. 检查目录权限: `ls -la /path/to/directory`
2. 修改权限: `chmod 755 /path/to/directory`
3. 或使用有权限的目录

### 问题2: 数据库文件不存在

**症状**: 仪表板显示空数据

**解决方案**:
1. 运行配置工具测试数据库访问
2. 检查路径配置是否正确
3. 手动初始化数据库

### 问题3: 配置不生效

**症状**: 仍然使用旧的数据库路径

**解决方案**:
1. 重启应用程序
2. 检查环境变量是否正确设置
3. 验证配置文件格式

## 迁移现有数据

如果需要迁移现有的数据库数据：

```bash
# 1. 备份现有数据库
cp plugins/builtin/dashboard_web/data/dashboard.db backup_dashboard.db

# 2. 设置新路径
export RUNSIM_DB_PATH=/new/path/dashboard.db

# 3. 复制数据库文件
cp backup_dashboard.db /new/path/dashboard.db
```

## 配置验证

使用以下命令验证配置：

```bash
cd plugins/builtin/dashboard_web
python configure_database_path.py
# 选择 "1. 查看当前配置" 和 "3. 测试数据库访问"
```

## 注意事项

1. **备份重要**: 修改数据库路径前请备份现有数据
2. **权限检查**: 确保目标目录有读写权限
3. **路径格式**: 使用正确的路径分隔符 (Windows: `\`, Linux/Mac: `/`)
4. **环境变量**: 环境变量设置后需要重启应用程序
5. **相对路径**: 相对路径基于当前工作目录，可能因启动位置而变化

## 技术支持

如果遇到问题，请：

1. 运行配置工具进行诊断
2. 检查应用程序日志
3. 验证文件系统权限
4. 联系技术支持团队
