#!/usr/bin/env python3
"""
Dashboard Web 配置文件

包含仪表板Web应用的所有配置项
"""

import os
from datetime import timedelta

# 获取当前文件所在目录
BASE_DIR = os.path.dirname(os.path.abspath(__file__))

def get_database_path():
    """
    获取数据库文件路径

    优先级顺序：
    1. 环境变量 RUNSIM_DB_PATH
    2. 配置文件 database_config.json
    3. 当前工作目录下的 runsim_dashboard.db
    4. 默认路径（向后兼容）

    Returns:
        str: 数据库文件的完整路径
    """
    # 1. 检查环境变量
    env_db_path = os.environ.get('RUNSIM_DB_PATH')
    if env_db_path:
        # 如果是目录，则在目录下创建数据库文件
        if os.path.isdir(env_db_path):
            return os.path.join(env_db_path, 'runsim_dashboard.db')
        # 如果是文件路径，直接使用
        return env_db_path

    # 2. 检查配置文件
    config_file = os.path.join(BASE_DIR, 'database_config.json')
    if os.path.exists(config_file):
        try:
            import json
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            config_path = config_data.get('database', {}).get('path', '').strip()
            if config_path:
                # 支持相对路径和绝对路径
                if not os.path.isabs(config_path):
                    config_path = os.path.join(os.getcwd(), config_path)

                # 如果是目录，则在目录下创建数据库文件
                if os.path.isdir(config_path):
                    return os.path.join(config_path, 'runsim_dashboard.db')
                # 如果是文件路径，直接使用
                return config_path
        except (json.JSONDecodeError, KeyError, IOError):
            # 配置文件格式错误，继续使用其他方式
            pass

    # 3. 使用当前工作目录
    cwd_db_path = os.path.join(os.getcwd(), 'runsim_dashboard.db')

    # 检查当前工作目录是否有写权限
    try:
        # 尝试在当前目录创建测试文件
        test_file = os.path.join(os.getcwd(), '.runsim_write_test')
        with open(test_file, 'w') as f:
            f.write('test')
        os.remove(test_file)
        return cwd_db_path
    except (OSError, IOError, PermissionError):
        # 如果当前目录没有写权限，使用默认路径
        pass

    # 4. 使用默认路径（向后兼容）
    return os.path.join(BASE_DIR, 'data', 'dashboard.db')

def get_database_backup_dir():
    """
    获取数据库备份目录

    Returns:
        str: 数据库备份目录路径
    """
    db_path = get_database_path()
    return os.path.dirname(db_path)

class Config:
    """基础配置类"""

    # 基础路径配置
    BASE_DIR = BASE_DIR

    # Flask配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'runsim-dashboard-secret-key-2024'

    # 数据库配置 - 使用动态路径
    DATABASE_PATH = get_database_path()
    DATABASE_BACKUP_DIR = get_database_backup_dir()
    
    # 上传文件配置
    UPLOAD_FOLDER = os.path.join(BASE_DIR, 'static', 'uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    ALLOWED_EXTENSIONS = {'xlsx', 'xls', 'csv'}
    
    # 导出文件配置
    EXPORT_FOLDER = os.path.join(BASE_DIR, 'data', 'exports')
    
    # 缓存配置
    CACHE_DIR = os.path.join(BASE_DIR, 'cache')
    CACHE_TIMEOUT = 300  # 5分钟
    
    # 分页配置
    CASES_PER_PAGE = 50
    BUGS_PER_PAGE = 20
    
    # 实时更新配置
    REALTIME_UPDATE_INTERVAL = 30  # 秒
    WEBSOCKET_ENABLED = True
    
    # 性能优化配置
    ENABLE_GZIP = True
    ENABLE_CACHE = True
    ENABLE_MINIFY = False  # 开发环境关闭
    
    # 日志配置
    LOG_LEVEL = 'INFO'
    LOG_FILE = os.path.join(BASE_DIR, 'logs', 'dashboard.log')
    LOG_MAX_SIZE = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT = 5
    
    # 安全配置
    CSRF_ENABLED = True
    SESSION_TIMEOUT = timedelta(hours=24)
    
    # API配置
    API_RATE_LIMIT = "1000 per hour"
    API_TIMEOUT = 30  # 秒
    
    # 图表配置
    CHART_COLORS = {
        'primary': '#007bff',
        'success': '#28a745',
        'danger': '#dc3545',
        'warning': '#ffc107',
        'info': '#17a2b8',
        'secondary': '#6c757d'
    }
    
    # 验证阶段配置
    VERIFICATION_PHASES = ['DVR1', 'DVR2', 'DVR3', 'DVS1', 'DVS2']
    
    # 用例状态配置
    CASE_STATUSES = ['PASS', 'Pending', 'On-Going', 'N/A']
    
    # BUG状态配置
    BUG_STATUSES = ['Open', 'In Progress', 'Fixed', 'Closed', 'Rejected']
    
    # 优先级配置
    BUG_PRIORITIES = ['Low', 'Medium', 'High', 'Critical']
    
    # 严重程度配置
    BUG_SEVERITIES = ['Minor', 'Major', 'Critical', 'Blocker']

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    TESTING = False

    # 开发环境特定配置
    REALTIME_UPDATE_INTERVAL = 10  # 更频繁的更新
    LOG_LEVEL = 'DEBUG'
    ENABLE_MINIFY = False

    # 开发数据库 - 使用动态路径，但添加_dev后缀
    def __init__(self):
        base_path = get_database_path()
        if base_path.endswith('.db'):
            self.DATABASE_PATH = base_path.replace('.db', '_dev.db')
        else:
            self.DATABASE_PATH = base_path + '_dev'

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    TESTING = False
    
    # 生产环境特定配置
    ENABLE_MINIFY = True
    LOG_LEVEL = 'WARNING'
    
    # 更严格的安全配置
    SESSION_TIMEOUT = timedelta(hours=8)
    API_RATE_LIMIT = "500 per hour"

class TestingConfig(Config):
    """测试环境配置"""
    DEBUG = True
    TESTING = True
    
    # 测试数据库
    DATABASE_PATH = ':memory:'  # 内存数据库
    
    # 测试特定配置
    CSRF_ENABLED = False
    REALTIME_UPDATE_INTERVAL = 1
    CACHE_TIMEOUT = 1

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

def get_config(config_name=None):
    """
    获取配置对象
    
    Args:
        config_name: 配置名称 ('development', 'production', 'testing')
        
    Returns:
        Config: 配置对象
    """
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'default')
    
    return config.get(config_name, config['default'])

# 创建必要的目录
def ensure_directories():
    """确保必要的目录存在"""
    directories = [
        Config.UPLOAD_FOLDER,
        Config.EXPORT_FOLDER,
        Config.CACHE_DIR,
        os.path.dirname(Config.LOG_FILE),
        Config.DATABASE_BACKUP_DIR
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)

# 验证配置
def validate_config(config_obj):
    """
    验证配置的有效性
    
    Args:
        config_obj: 配置对象
        
    Returns:
        bool: 配置是否有效
    """
    try:
        # 检查必要的目录
        ensure_directories()
        
        # 检查数据库路径
        if config_obj.DATABASE_PATH != ':memory:':
            db_dir = os.path.dirname(config_obj.DATABASE_PATH)
            if not os.path.exists(db_dir):
                os.makedirs(db_dir, exist_ok=True)
        
        # 检查上传文件大小限制
        if config_obj.MAX_CONTENT_LENGTH <= 0:
            raise ValueError("MAX_CONTENT_LENGTH must be positive")
        
        # 检查分页配置
        if config_obj.CASES_PER_PAGE <= 0 or config_obj.BUGS_PER_PAGE <= 0:
            raise ValueError("Page size must be positive")
        
        return True
        
    except Exception as e:
        print(f"配置验证失败: {e}")
        return False

# 导出常用配置
__all__ = [
    'Config',
    'DevelopmentConfig', 
    'ProductionConfig',
    'TestingConfig',
    'config',
    'get_config',
    'ensure_directories',
    'validate_config'
]
